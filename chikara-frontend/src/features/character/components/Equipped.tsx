import type { EquippedItems } from "@/types/item";
import EmptyCell from "../../../components/Inventory/EmptyCell";
import GearCell from "../../../components/Inventory/GearCell";
import EquippedStats from "./EquippedStats";

export default function Equipped({
    equippedItems,
    setEquipTooltipFilter,
    isBattle = false,
    mobile = false,
}: {
    equippedItems: EquippedItems;
    setEquipTooltipFilter?: any;
    isBattle?: boolean;
    mobile?: boolean;
}) {
    return (
        <>
            {!mobile ? (
                // Desktop version
                <div className="mb-6 px-4 py-2 ring-indigo-600 md:mb-0 md:rounded-lg md:bg-gray-800 md:shadow-md md:ring-1 2xl:min-h-154">
                    <div className="grid grid-cols-6 gap-2">
                        <EmptyCell large />
                        <GearCell
                            type="Head"
                            itemObj={equippedItems?.head || null}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <EmptyCell large />
                        <GearCell
                            type="Melee Weapon"
                            itemObj={equippedItems?.weapon || null}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <GearCell
                            type="Chest"
                            itemObj={equippedItems?.chest || null}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <GearCell
                            type="Offhand"
                            itemObj={equippedItems?.offhand || null}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <GearCell
                            type="Ranged Weapon"
                            itemObj={equippedItems?.ranged || null}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <GearCell
                            type="Hands"
                            itemObj={equippedItems?.hands || null}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <EmptyCell large />
                        <EmptyCell large />
                        <GearCell
                            type="Legs"
                            itemObj={equippedItems?.legs || null}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <EmptyCell large />
                        <EmptyCell large />
                        <GearCell
                            type="Feet"
                            itemObj={equippedItems?.feet || null}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <EmptyCell />
                        <EmptyCell />
                        <GearCell
                            type="Ring"
                            itemObj={equippedItems?.finger || null}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <EmptyCell large />
                        <EmptyCell large />
                        <EmptyCell />

                        <EquippedStats isBattle={isBattle} equippedItems={equippedItems} />
                    </div>
                </div>
            ) : (
                // Mobile version
                <div className="p-2">
                    <div className="mx-auto grid w-5/6 grid-cols-6 gap-2">
                        <EmptyCell mobile large />
                        <GearCell
                            mobile
                            type="Head"
                            itemObj={equippedItems?.head}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <EmptyCell mobile large />
                        <GearCell
                            mobile
                            type="Weapon"
                            itemObj={equippedItems?.weapon}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <GearCell
                            mobile
                            type="Chest"
                            itemObj={equippedItems?.chest}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <GearCell
                            mobile
                            type="Offhand"
                            itemObj={equippedItems?.offhand}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <GearCell
                            mobile
                            type="Ranged Weapon"
                            itemObj={equippedItems?.ranged}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <GearCell
                            mobile
                            type="Hands"
                            itemObj={equippedItems?.hands}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <EmptyCell mobile large />
                        <EmptyCell mobile large />
                        <GearCell
                            mobile
                            type="Legs"
                            itemObj={equippedItems?.legs}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <EmptyCell mobile large />
                        <EmptyCell mobile large />
                        <GearCell
                            mobile
                            type="Feet"
                            itemObj={equippedItems?.feet}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <EmptyCell mobile />
                        <EmptyCell mobile />
                        <GearCell
                            mobile
                            type="Ring"
                            itemObj={equippedItems?.finger}
                            setEquipTooltipFilter={setEquipTooltipFilter}
                            isBattle={isBattle}
                        />
                        <EmptyCell mobile large />
                        <EmptyCell mobile large />
                        <EmptyCell mobile />
                    </div>
                    <EquippedStats isBattle={isBattle} equippedItems={equippedItems || []} />
                </div>
            )}
        </>
    );
}
