import { describe, expect, it } from "vitest";
import { itemStatDisplay } from "../itemStatDisplay";
import type { Item } from "@/types/item";

describe("itemStatDisplay", () => {
    const mockMetabolismTalent = { modifier: 1.1 };

    it("should handle items with itemEffects stat modifiers", () => {
        const mockItem: Partial<Item> = {
            itemType: "weapon",
            damage: 100,
            armour: 50,
            itemEffects: [
                {
                    effectKey: "strength",
                    effectModifier: "multiply",
                    effectValue: 1.15, // 15% bonus
                    effectGroup: "statModifiers",
                    description: "Increases Strength by 15%"
                },
                {
                    effectKey: "defence",
                    effectModifier: "multiply", 
                    effectValue: 1.10, // 10% bonus
                    effectGroup: "statModifiers",
                    description: "Increases Defence by 10%"
                }
            ]
        };

        const result = itemStatDisplay(mockItem as Item, mockMetabolismTalent, 0, false, false);
        
        // Should include damage, armour, and stat modifiers
        expect(result).toContain("100 DMG");
        expect(result).toContain("50 ARMOR");
        expect(result).toContain("15 % STR");
        expect(result).toContain("10 % DEF");
    });

    it("should handle items without itemEffects", () => {
        const mockItem: Partial<Item> = {
            itemType: "weapon",
            damage: 100,
            armour: 50,
            itemEffects: []
        };

        const result = itemStatDisplay(mockItem as Item, mockMetabolismTalent, 0, false, false);
        
        // Should only include damage and armour, no stat modifiers
        expect(result).toContain("100 DMG");
        expect(result).toContain("50 ARMOR");
        expect(result.filter(stat => stat?.includes("% STR"))).toHaveLength(0);
        expect(result.filter(stat => stat?.includes("% DEF"))).toHaveLength(0);
    });

    it("should filter out non-statModifiers effects", () => {
        const mockItem: Partial<Item> = {
            itemType: "weapon",
            damage: 100,
            itemEffects: [
                {
                    effectKey: "strength",
                    effectModifier: "multiply",
                    effectValue: 1.15,
                    effectGroup: "statModifiers",
                    description: "Increases Strength by 15%"
                },
                {
                    effectKey: "healing",
                    effectModifier: "add",
                    effectValue: 50,
                    effectGroup: "treatment", // Different group
                    description: "Heals 50 HP"
                }
            ]
        };

        const result = itemStatDisplay(mockItem as Item, mockMetabolismTalent, 0, false, false);
        
        // Should include strength modifier but not healing effect
        expect(result).toContain("100 DMG");
        expect(result).toContain("15 % STR");
        expect(result.filter(stat => stat?.includes("healing"))).toHaveLength(0);
    });

    it("should handle health modifier for finger items correctly", () => {
        const mockItem: Partial<Item> = {
            itemType: "finger",
            itemEffects: [
                {
                    effectKey: "health",
                    effectModifier: "add",
                    effectValue: 100, // Direct health value for rings
                    effectGroup: "statModifiers",
                    description: "Increases Health by 100"
                }
            ]
        };

        const result = itemStatDisplay(mockItem as Item, mockMetabolismTalent, 0, false, false);
        
        // For finger items, health should be displayed as direct value, not percentage
        expect(result).toContain("100  HP");
    });

    it("should handle null/undefined itemEffects", () => {
        const mockItem: Partial<Item> = {
            itemType: "weapon",
            damage: 100,
            itemEffects: null
        };

        const result = itemStatDisplay(mockItem as Item, mockMetabolismTalent, 0, false, false);
        
        // Should only include damage, no stat modifiers
        expect(result).toContain("100 DMG");
        expect(result.filter(stat => stat?.includes("%"))).toHaveLength(0);
    });
});
