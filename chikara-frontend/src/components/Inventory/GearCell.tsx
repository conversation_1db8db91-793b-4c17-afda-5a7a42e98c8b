import { DisplayItem } from "@/components/DisplayItem";
import LoadingState from "@/components/LoadingState";
import { cn } from "@/lib/utils";
import { Button } from "@headlessui/react";
import { X } from "lucide-react";
import { useGetEquippedItems } from "@/hooks/api/useGetEquippedItems";
import { useUnequipItem } from "@/hooks/api/useUnequipItem";
import { EquippedItem } from "@/types/item";

const getItemType = (type: string) => {
    switch (type) {
        case "Head":
            return "head";
        case "Weapon":
            return "weapon";
        case "Ranged Weapon":
            return "ranged";
        case "Chest":
            return "chest";
        case "Hands":
            return "hands";
        case "Legs":
            return "legs";
        case "Feet":
            return "feet";
        case "Ring":
            return "finger";
        case "Offhand":
            return "shield";
    }
};

export default function GearCell({
    mobile = false,
    type,
    itemObj,
    setEquipTooltipFilter,
    isBattle = false,
}: {
    mobile?: boolean;
    type: string;
    itemObj: EquippedItem | null;
    setEquipTooltipFilter: any;
    isBattle: boolean;
}) {
    const unequipItemMutation = useUnequipItem();

    const { isLoading } = useGetEquippedItems({
        enabled: !isBattle,
    });

    const unequipItem = (item: EquippedItem | null) => {
        if (!item || isBattle) return;
        unequipItemMutation.mutate({ slot: item.itemType });
    };

    const showEquipTooltip = (itemType: string) => {
        if (isBattle) return;
        setEquipTooltipFilter(getItemType(itemType));
    };

    if (mobile)
        return (
            <div
                className={`gridIcon col-span-2 flex h-16 w-full rounded-md bg-slate-900 p-0.5 text-gray-100 text-stroke-md ring-2 ring-indigo-600 drop-shadow-md ${
                    itemObj !== null && `gridIconHover hover:cursor-pointer hover:bg-slate-600`
                }`}
            >
                <LoadingState size={8} isLoading={isLoading && !isBattle}>
                    {!isBattle && (
                        <X
                            className="absolute z-15 size-6 cursor-pointer rounded-md border border-gray-800 bg-gray-700 text-red-500"
                            onClick={() => unequipItem(itemObj)}
                        />
                    )}
                    {itemObj === null ? (
                        <Button
                            data-tooltip-id="equip-tooltip"
                            className="m-auto text-center text-sm select-none!"
                            onClick={() => showEquipTooltip(type)}
                        >
                            <p>{type}</p>
                            <small className="text-gray-400">Empty</small>
                        </Button>
                    ) : (
                        <DisplayItem
                            noBackground
                            isClickable
                            disableTooltip={!isBattle}
                            className="m-auto size-12"
                            item={itemObj}
                        />
                    )}
                </LoadingState>
            </div>
        );

    return (
        <div
            className={`gridIcon col-span-2 flex h-20 w-full rounded-md bg-slate-900 p-1 text-gray-100 text-stroke-md ring-2 ring-indigo-600 drop-shadow-md ${
                itemObj !== null && `gridIconHover hover:bg-slate-600`
            }`}
        >
            <LoadingState size={8} isLoading={isLoading}>
                {!isBattle && (
                    <X
                        className="absolute z-15 size-6 cursor-pointer rounded-md border border-gray-800 bg-gray-700 text-red-500 hover:bg-gray-800"
                        onClick={() => unequipItem(itemObj)}
                    />
                )}

                {itemObj === null ? (
                    <Button
                        data-tooltip-id="equip-tooltip"
                        className="m-auto cursor-pointer select-none text-center hover:text-custom-yellow"
                        onClick={() => showEquipTooltip(type)}
                    >
                        <p className={cn(type.length > 10 ? "text-sm" : "text-base")}>{type}</p>
                        <small className="text-gray-400">Empty</small>
                    </Button>
                ) : (
                    <div
                        data-tooltip-id="equip-tooltip"
                        className="relative flex size-full cursor-pointer"
                        onClick={() => (isBattle ? null : showEquipTooltip(type))}
                    >
                        {itemObj?.upgradeLevel > 0 && (
                            <p className="absolute top-0 left-1 text-custom-yellow text-sm">+{itemObj?.upgradeLevel}</p>
                        )}

                        <DisplayItem
                            noBackground
                            isClickable
                            disableTooltip={!isBattle}
                            className="m-auto size-16"
                            item={itemObj}
                        />
                    </div>
                )}
            </LoadingState>
        </div>
    );
}
